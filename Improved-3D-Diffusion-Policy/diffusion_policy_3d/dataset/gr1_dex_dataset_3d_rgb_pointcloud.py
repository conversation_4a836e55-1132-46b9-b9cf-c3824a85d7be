from typing import Dict, List, Optional
import torch
import numpy as np
import copy
from diffusion_policy_3d.common.pytorch_util import dict_apply
from diffusion_policy_3d.common.replay_buffer import ReplayBuffer
from diffusion_policy_3d.common.sampler import (SequenceSampler, get_val_mask, downsample_mask)
from diffusion_policy_3d.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer, StringNormalizer
from diffusion_policy_3d.dataset.base_dataset import BaseDataset
import diffusion_policy_3d.model.vision_3d.point_process as point_process
from termcolor import cprint

class GR1DexDataset3DCustom(BaseDataset):
    def __init__(self,
            zarr_path, 
            horizon=1,
            pad_before=0,
            pad_after=0,
            seed=42,
            val_ratio=0.0,
            max_train_episodes=None,
            task_name=None,
            num_points=4096,
            custom_val_episodes: Optional[List[int]] = None,
            custom_train_episodes: Optional[List[int]] = None,
            ):
        super().__init__()
        cprint(f'Loading GR1DexDataset3DCustom from {zarr_path}', 'green')
        self.task_name = task_name
        self.num_points = num_points

        buffer_keys = [
            'state', 
            'action',]
        
        buffer_keys.append('point_cloud')
            
        self.replay_buffer = ReplayBuffer.copy_from_path(
            zarr_path, keys=buffer_keys)
        
        # Custom validation/training episode selection
        if custom_val_episodes is not None or custom_train_episodes is not None:
            val_mask = self._create_custom_mask(custom_val_episodes, custom_train_episodes)
            cprint(f'Using custom episode selection:', 'yellow')
            if custom_val_episodes is not None:
                cprint(f'  Validation episodes: {custom_val_episodes}', 'cyan')
            if custom_train_episodes is not None:
                cprint(f'  Training episodes: {custom_train_episodes}', 'cyan')
        else:
            # Default random selection
            val_mask = get_val_mask(
                n_episodes=self.replay_buffer.n_episodes, 
                val_ratio=val_ratio,
                seed=seed)
            cprint(f'Using random episode selection (val_ratio={val_ratio}, seed={seed}):', 'yellow')
            cprint(f'  Validation episodes: {np.where(val_mask)[0]}', 'cyan')
            cprint(f'  Training episodes: {np.where(~val_mask)[0]}', 'cyan')
        
        train_mask = ~val_mask
        train_mask = downsample_mask(
            mask=train_mask, 
            max_n=max_train_episodes, 
            seed=seed)
        
        self.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=horizon,
            pad_before=pad_before, 
            pad_after=pad_after,
            episode_mask=train_mask)
        self.train_mask = train_mask
        self.val_mask = val_mask
        self.horizon = horizon
        self.pad_before = pad_before
        self.pad_after = pad_after

    def _create_custom_mask(self, custom_val_episodes, custom_train_episodes):
        """Create validation mask based on custom episode selection"""
        n_episodes = self.replay_buffer.n_episodes
        val_mask = np.zeros(n_episodes, dtype=bool)
        
        if custom_val_episodes is not None:
            # Validate episode indices
            for ep_idx in custom_val_episodes:
                if ep_idx >= n_episodes or ep_idx < 0:
                    raise ValueError(f"Invalid validation episode index {ep_idx}. "
                                   f"Dataset has {n_episodes} episodes (0-{n_episodes-1})")
            val_mask[custom_val_episodes] = True
        
        if custom_train_episodes is not None:
            # Validate episode indices
            for ep_idx in custom_train_episodes:
                if ep_idx >= n_episodes or ep_idx < 0:
                    raise ValueError(f"Invalid training episode index {ep_idx}. "
                                   f"Dataset has {n_episodes} episodes (0-{n_episodes-1})")
            # If both are specified, ensure no overlap
            if custom_val_episodes is not None:
                overlap = set(custom_val_episodes) & set(custom_train_episodes)
                if overlap:
                    raise ValueError(f"Episodes {overlap} specified in both training and validation sets")
        
        return val_mask

    def get_validation_dataset(self):
        val_set = copy.copy(self)
        val_set.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=self.horizon,
            pad_before=self.pad_before, 
            pad_after=self.pad_after,
            episode_mask=self.val_mask
            )
        val_set.train_mask = self.val_mask
        return val_set

    def get_normalizer(self, mode='limits', **kwargs):
        data = {'action': self.replay_buffer['action']}
        normalizer = LinearNormalizer()
        normalizer.fit(data=data, last_n_dims=1, mode=mode, **kwargs)

        normalizer['point_cloud'] = SingleFieldLinearNormalizer.create_identity()
        normalizer['agent_pos'] = SingleFieldLinearNormalizer.create_identity()
        
        return normalizer

    def __len__(self) -> int:
        return len(self.sampler)

    def _sample_to_data(self, sample):
        agent_pos = sample['state'][:,].astype(np.float32)
        point_cloud = sample['point_cloud'][:,].astype(np.float32)
        point_cloud = point_process.uniform_sampling_numpy(point_cloud, self.num_points)
        data = {
            'obs': {
                'agent_pos': agent_pos,
                'point_cloud': point_cloud,
                },
            'action': sample['action'].astype(np.float32)}
           
        return data
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.sampler.sample_sequence(idx)
        data = self._sample_to_data(sample)
        to_torch_function = lambda x: torch.from_numpy(x) if x.__class__.__name__ == 'ndarray' else x
        torch_data = dict_apply(data, to_torch_function)
        return torch_data

    def get_episode_info(self):
        """Get information about all episodes"""
        episode_info = []
        for i in range(self.replay_buffer.n_episodes):
            episode = self.replay_buffer.get_episode(i)
            episode_info.append({
                'episode_id': i,
                'length': len(episode['action']),
                'is_validation': self.val_mask[i],
                'is_training': self.train_mask[i] if hasattr(self, 'train_mask') else ~self.val_mask[i]
            })
        return episode_info
