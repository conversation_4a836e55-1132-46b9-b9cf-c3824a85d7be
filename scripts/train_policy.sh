# Examples:

#   bash scripts/train_policy.sh idp3 gr1_dex-3d 0913_example
#   bash scripts/train_policy.sh dp_224x224_r3m gr1_dex-image 0913_example
#   bash scripts/train_policy.sh idp3_pointcloud gr1_dex-3d pointcloud_training
#   bash scripts/train_policy.sh idp3_pointcloud_debug gr1_dex-3d single_episode_debug

dataset_path=/home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/raw_pour_converted


DEBUG=False
wandb_mode=offline


alg_name=${1}
task_name=${2}
config_name=${alg_name}
addition_info=${3}
seed=${4:-0}  # 允许通过第4个参数设置seed，默认为0
exp_name=${task_name}-${alg_name}-${addition_info}
run_dir="data/outputs/${exp_name}_seed${seed}"

# 点云训练的特殊参数设置
max_train_episodes=${5:-"null"}  # 第5个参数：训练episode数量，默认为null（使用所有）
val_ratio=${6:-0.1}              # 第6个参数：验证集比例，默认为0.1
checkpoint_every=${7:-100}        # 第7个参数：checkpoint保存间隔，默认为100
batch_size=${8:-96}              # 第8个参数：batch size，默认为96

gpu_id=0
echo -e "\033[33mgpu id (to use): ${gpu_id}\033[0m"
echo -e "\033[33mmax_train_episodes: ${max_train_episodes}\033[0m"
echo -e "\033[33mval_ratio: ${val_ratio}\033[0m"
echo -e "\033[33mcheckpoint_every: ${checkpoint_every}\033[0m"
echo -e "\033[33mbatch_size: ${batch_size}\033[0m"


if [ $DEBUG = True ]; then
    save_ckpt=False
    # wandb_mode=online
    echo -e "\033[33mDebug mode!\033[0m"
    echo -e "\033[33mDebug mode!\033[0m"
    echo -e "\033[33mDebug mode!\033[0m"
else
    save_ckpt=True
    echo -e "\033[33mTrain mode\033[0m"
fi


cd Improved-3D-Diffusion-Policy

export HYDRA_FULL_ERROR=1 
export CUDA_VISIBLE_DEVICES=${gpu_id}

python train.py --config-name=${config_name}.yaml \
                            task=${task_name} \
                            hydra.run.dir=${run_dir} \
                            training.debug=$DEBUG \
                            training.seed=${seed} \
                            training.device="cuda:0" \
                            exp_name=${exp_name} \
                            logging.mode=${wandb_mode} \
                            checkpoint.save_ckpt=${save_ckpt} \
                            training.checkpoint_every=${checkpoint_every} \
                            dataloader.batch_size=${batch_size} \
                            val_dataloader.batch_size=${batch_size} \
                            task.dataset.zarr_path=$dataset_path \
                            task.dataset.max_train_episodes=${max_train_episodes} \
                            task.dataset.val_ratio=${val_ratio}



                                